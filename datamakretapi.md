Big picture
Goal: Provide a simple public API that returns STAC items and COG headers by date/filters so clients (using the rasteret library) can directly compute HTTP byte ranges and read tiles without building local parquet indexes.
Internals like observability, snapshot monitoring, connection pools are for us; the public API must be small, predictable, and fast for users.
You asked for two viewpoints. Below is a structured write-up: what’s in place, what’s pending, and how the rasteret “query side” informs the API shape.

What we’ve built so far (maintainer view)
FastAPI app scaffolding, CORS, health and basic endpoints
Global service initialization with safety limits, CORS, and versions
Health and table listing/info are wired up
DuckDB service for querying Delta tables
Connection pooling, query timeouts, result size limits, and Arrow streaming paths
S3/httpfs/cache_httpfs extension setup and memory/thread controls
Monitoring and snapshot infrastructure
Prometheus metrics endpoint and a snapshot monitor hook (internal ops)
STAC/COG parsing capability
A STAC-aware wrapper around a high-performance COG header parser, designed to parse many asset headers concurrently and enrich with scale/offset if present
Examples of current code:


endpoints.py
src/data_marketplace/api

stac_cog_processor.py
src/data_marketplace/cog
What’s pending (maintainer view)
Public API surface focused on STAC/COG headers:

Define request/response models for:
Filter params: date_range, bbox, collections, cloud cover, asset keys, limit/offset
Returned shapes: STAC item summary and per-asset COG header record
Endpoints:
GET /v1/items: returns STAC-like item summaries (id, datetime, bbox, collection)
GET /v1/items:cog-headers: runs the same filters and returns per-asset COG header records
GET /v1/assets/cog-headers?href=...: single-asset COG header for quick probes
Query builder layer to translate filters to SQL against our Delta tables, using the DuckDBService
Table alias selection and schema expectations (scene_id, collection, datetime, bbox, cog_key/href)
Pagination and safety limits
Integration: for each matched item, call StacCogProcessor to get COG headers (batch-friendly) and stream results
Performance/Resilience:
Concurrency caps for header parsing; backpressure
Optional caching of header results by URL+ETag/Last-Modified (server-side)
Error handling: timeouts, partial failures, per-asset error reporting
Tests:
Unit tests for models and query builder
Integration tests for endpoints with mocked DuckDB and mocked COGHeaderParser
Docs/examples for users (how to plug API output into rasteret)
What users need (user view)
Simple filters to select scenes:
date_range, bbox, collections, optional cloud_cover and asset selection
Two kinds of outputs:
Item summaries (for discovery): id, datetime, bbox, collection
COG header records per asset (for fetching): the exact metadata needed to compute ranges and decode tiles
They explicitly do not want to maintain local parquet; they just want headers (and basic STAC info).

Understanding the rasteret “query side” and how it informs our API
The rasteret client code that reads data needs these pieces to compute HTTP byte ranges and decode tiles:

The COG URL (ideally an HTTP URL; if client only has s3://, they or we need to convert)
CogMetadata fields:
width, height
tile_width, tile_height
dtype
crs
predictor
transform (the simplified [scale_x, translate_x, scale_y, translate_y] form your fetcher uses)
tile_offsets (array)
tile_byte_counts (array)
pixel_scale, tiepoint (optional but useful)
Optional band scale/offset from STAC extensions
Asset identity info: item_id, asset_key, collection, datetime
Optional: roles, media_type/title (nice-to-have)
The rasteret fetcher computes merged HTTP ranges and decodes tiles from those fields. Returning this structure directly from the API means the user can immediately call rasteret.fetch.cog.read_cog_tile_data(url, CogMetadata(...), geometry=...) with no extra lookups.

Small example client flow:

Call /v1/items:cog-headers with filters and assets=red,nir08
For each record, construct rasteret.types.CogMetadata and call read_cog_tile_data
Proposed minimal public API (user-facing)
GET /v1/items
Query params: collections, date_start, date_end, bbox, cloud_cover_max, limit, offset
Returns: array of items: {item_id, collection, datetime, bbox}
GET /v1/items:cog-headers
Same filters; optional assets=comma list (e.g., red,nir08)
Returns NDJSON or JSON array of per-asset COG header records:
item_id, collection, datetime, bbox
asset_key, media_type, roles
cog_href (original), request_url (http(s) form for range requests)
width, height, tile_width, tile_height, dtype, crs, predictor, transform
tile_offsets[], tile_byte_counts[], pixel_scale, tiepoint
cog_scale, cog_offset (if available)
GET /v1/assets/cog-headers?href=... (single asset probe)
Returns one COG header record or error
Notes:

Default format JSON; consider ndjson for streaming large sets
Pagination via limit/offset
Auth handled upstream by you; we keep it public endpoints-agnostic
Proposed internal shapes (maintainer-facing)
Pydantic models:

Filters model: collections: List[str], date_range: (str, str), bbox: 4 floats, cloud_cover_max: float, assets: Optional[List[str]], limit/offset
ItemSummary: item_id, collection, datetime, bbox
CogHeaderRecord: fields listed above (aligned with rasteret.types.CogMetadata)
Endpoint handlers:

Use DuckDBService to query the attached “items” table with filters
For items endpoint: return ItemSummary rows
For cog-headers endpoint: build a small STAC item view per row (or directly pass url list) and call StacCogProcessor.parse_cog_headers_for_item or per-asset method, honoring max_concurrent
Provide request_url computed via StacCogProcessor.compute_request_href; also return original cog_href
Caching (optional but recommended):

LRU cache for COG header dict keyed by request_url + ETag/Last-Modified when available; TTL fallback if headers don’t expose validators
Observability (internal):

Count items matched, assets processed, header parse latency, cache hit/miss
Error taxonomy per-asset; partial success returns continue
Examples (user view)
Discover scenes:
GET /v1/items?collections=SENTINEL2&date_start=2024-01-01&date_end=2024-01-31&bbox=-123.2,49.1,-122.9,49.3&limit=100
Fetch headers for selected bands:
GET /v1/items:cog-headers?collections=SENTINEL2&date_start=2024-01-01&date_end=2024-01-31&assets=red,nir08&limit=50
Use with rasteret:
Python sketch:

For each returned record, construct rasteret.types.CogMetadata with those fields and call rasteret.fetch.cog.read_cog_tile_data(request_url, metadata, geometry=...)
What we should decide next
Confirm the Delta table schema/alias to query for scenes and assets in DuckDB (column names for collection, datetime, bbox, cog hrefs)
Agree on the exact response fields to match rasteret CogMetadata names (no translation required on client)
Choose response format for large results (JSON array vs NDJSON; recommend NDJSON or Arrow if we later need columnar)
Concurrency defaults for header parsing and sensible per-request caps
Whether we want to expose an option include=item_summaries_only vs include=cog_headers to let users first explore items, then fetch headers
If you’d like, I can draft the concrete request/response models and endpoint skeletons next, aligned exactly to the rasteret CogMetadata dataclass, and include a tiny end-to-end mock test to validate shapes.