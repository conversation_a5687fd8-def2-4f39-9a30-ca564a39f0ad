Big picture
Goal: a small, predictable public API that returns STAC item summaries and per-asset COG headers from filters (date range, bbox, collections, etc.). Users will plug these results straight into rasteret’s query-side functions to compute HTTP ranges and read tiles. Users ideally won’t build local parquet indices.
Internals (DuckDB, monitoring, snapshot mgmt, caching, limits) matter for us; users just need simple filters and correctly shaped metadata needed by rasteret’s fetcher.
What’s in place today (maintainer view)
FastAPI service scaffolded with CORS, health, table management:
endpoints.py exposes:
/health
/q (ad‑hoc SELECT with Arrow or JSON)
/q/named (predefined NamedQuery templates)
/tables attach/detach/refresh/info/list
DuckDBService integrated with:
Safety limits (rows, bytes, time, concurrency)
Connection pooling
Arrow streaming
Ready for attaching Delta tables
Named query facility (QueryTemplate + PREDEFINED_QUERIES) for common SELECTs
STAC/COG parsing building blocks:
data_marketplace/cog/stac_cog_processor.py:
compute_request_href converting s3:// to https://bucket.s3.amazonaws.com/…
High-performance header parsing via COGHeaderParser (async, batch)
Extracts optional scale/offset from STAC raster:bands
Builds per-asset “COG record” dicts (fields include asset_key/href and parsed header fields)
What’s pending (maintainer view)
Public, user‑facing endpoints and models focused on items and per‑asset COG headers:
Request models for filters: collections, date_start/date_end, bbox, optional cloud_cover, assets filter, limit/offset
Response models aligned to rasteret.types.CogMetadata (see “Query-side constraints”)
Endpoints to implement:
GET /v1/items → Item summaries (id, collection, datetime, bbox)
GET /v1/items:cog-headers → Same filters; returns per‑asset COG header records
GET /v1/assets/cog-headers?href=… → Single‑asset probe
Query builder to translate filters → SQL against our attached Delta table(s)
Decide table alias and schema expectations (scene_id/item_id, collection, datetime, bbox, asset map or asset rows with cog hrefs)
Pagination and safety limits that play well with streaming (JSON array vs NDJSON)
Integration: for matched items/assets, invoke StacCogProcessor to parse headers at capped concurrency; stream results
Performance/resilience:
Concurrency caps and backpressure for header parsing
Optional caching (request_url + ETag/Last‑Modified keyed) with TTL fallback
Partial failure handling per asset (return per‑asset error without failing whole request)
Tests and docs:
Unit tests for request/response models and filter → SQL builder
Endpoint integration tests with mocked DuckDBService and COGHeaderParser
User examples showing how to pass API output to rasteret.fetch.cog.read_cog_tile_data
What users need (user view)
Simple filters: collections, date range, bbox; optionally cloud_cover and asset keys.
Two outputs:
Item discovery: {item_id, collection, datetime, bbox}
Fetchable per‑asset COG header records containing exactly the fields rasteret’s reader needs to compute HTTP ranges and read tiles.
Minimal friction:
Prefer request_url to be http/https even if original href was s3://
Field names/types should directly construct rasteret.types.CogMetadata without extra translation
JSON format; possibly NDJSON for large result sets
How rasteret’s query side constrains our API
Key references:

CogMetadata definition (rasteret/src/rasteret/types.py)
read_cog_tile_data and COGReader logic (rasteret/src/rasteret/fetch/cog.py)
What the client needs to compute ranges and decode tiles:

Required fields (align to CogMetadata):
width:int, height:int
tile_width:int, tile_height:int
dtype: machine-usable; rasteret accepts numpy dtype or Arrow DataType. Over JSON, provide a canonical string (e.g., "uint16"), and optionally an Arrow type string for machine-readability.
crs:int (EPSG code)
predictor: Optional[int] (Δ predictor used for decoding)
compression: Optional[int] but users also benefit from name string. Provide both code and name as separate fields.
transform: List[float], specifically [scale_x, translate_x, scale_y, translate_y] as used by compute_tile_indices. This is not the full 6‑element GDAL affine; rasteret expects this compact 4‑element form.
tile_offsets: List[int]
tile_byte_counts: List[int]
Optional pixel_scale, tiepoint (tuples) are helpful but not strictly required
Item/asset identity:
item_id, asset_key, collection, datetime, bbox
cog_href (original) and request_url (http(s) form to be used for Range requests)
Scale/offset from STAC:
Follow your preference: surface as cog_dn_scale and cog_dn_offset (not defaulting—null if not present).
Human-readable and machine-readable pairs:
compression: include compression_name and compression_code
predictor: include predictor_name and predictor_code (if available)
dtype: include dtype_numpy and dtype_arrow (strings)
rasteret specifics worth noting:

Tile selection uses compute_tile_indices with the 4‑element transform and tile sizes to derive intersecting tiles for AOIs.
HTTP range merging (gap_threshold) and HTTP/2 multiplexing reduce request count; we just need correct offsets/byte_counts and predictor/compression/dtype.
Geometry handling converts AOI to metadata.crs before tile computation; so we must provide integer EPSG (crs) and a consistent transform in that CRS.
s3:// vs http(s):
StacCogProcessor.compute_request_href will convert s3:// to https://… but its is_cog_asset currently accepts only http(s) hrefs. For s3‑only STAC catalogs, this filtering could skip assets. Either we relax is_cog_asset or perform conversion before filtering in our API layer.
Proposed minimal public API (user-facing)
GET /v1/items
Query params: collections, date_start, date_end, bbox, cloud_cover_max, limit, offset
Returns: [{item_id, collection, datetime, bbox}]
GET /v1/items:cog-headers
Same filters; optional assets=red,nir08
Returns: JSON array or NDJSON of per‑asset records:
Identity: item_id, collection, datetime, bbox, asset_key, asset_roles, asset_media_type, cog_href, request_url
CogMetadata fields: width, height, tile_width, tile_height, crs, transform (4‑element), tile_offsets[], tile_byte_counts[], dtype_* pairs, predictor_* pairs, compression_* pairs, pixel_scale, tiepoint
STAC-derived: cog_dn_scale, cog_dn_offset (null if missing)
GET /v1/assets/cog-headers?href=…
Returns one record for quick single‑asset probes
Notes:

JSON default; NDJSON preferred when result volumes are large (streaming friendly).
Pagination: limit/offset semantics with caps consistent with safety limits.
Auth is handled upstream by you.
Gaps and decisions to make
Table schema and alias to query:
Which Delta table(s) and columns store scenes and asset hrefs? We need a consistent way to map item rows → asset hrefs by key (or explode assets into rows).
Field naming and parity with rasteret:
Adopt cog_dn_scale, cog_dn_offset (your preference) instead of cog_scale/cog_offset.
Standardize machine + human readable pairs for dtype, compression, predictor.
s3‑only STAC assets:
Decide whether the API layer should always normalize hrefs to request_url before filtering so that is_cog_asset doesn’t silently drop s3 assets.
Response format for large results:
JSON array vs NDJSON; my recommendation is NDJSON for /items:cog-headers to enable stream processing in clients.
Concurrency defaults and per‑request caps for header parsing (e.g., 150–300 max_concurrent with proper resource cleanup).
Caching headers:
If ETag/Last‑Modified present, key cache on request_url + validator; TTL fallback otherwise.
Error reporting:
Per‑asset error objects included in stream rather than failing the whole response.
Suggested next steps
Confirm the Delta table(s) and schema we’ll query for item/asset selection.
Lock the exact response field names aligned to rasteret expectations:
Transform shape as [scale_x, translate_x, scale_y, translate_y]
crs as EPSG int
dtype_numpy + dtype_arrow strings
compression_code + compression_name; predictor_code + predictor_name
cog_dn_scale, cog_dn_offset (nullable)
Decide large-result format (JSON vs NDJSON).
If you agree, I can draft:
Pydantic request/response models for the three endpoints
Endpoint skeletons wiring: filters → SQL via DuckDBService → items → StacCogProcessor → stream JSON/NDJSON
A small integration test with mocks to validate shapes and streaming behavior
Quick examples (user view)
Discovery:
GET /v1/items?collections=SENTINEL2&date_start=2024-01-01&date_end=2024-01-31&bbox=-123.2,49.1,-122.9,49.3&limit=100
Headers:
GET /v1/items:cog-headers?collections=SENTINEL2&date_start=2024-01-01&date_end=2024-01-31&assets=red,nir08&limit=50
Using with rasteret:
For each returned record, construct CogMetadata using the provided fields, and call rasteret.fetch.cog.read_cog_tile_data(request_url, metadata, geometry=your_aoi).
My take on what we’ve done vs what remains
We have a solid service substrate (DuckDB + FastAPI + safety/observability) and a performant COG header parsing path. That’s the heavy lifting.
The public interface is what’s missing: filter models, item listing, and per‑asset header streaming aligned exactly to rasteret’s CogMetadata and fetch logic.
Once we lock the response naming and schema to match rasteret, implementation is straightforward: build a filter→SQL layer, glue in StacCogProcessor, and stream.
