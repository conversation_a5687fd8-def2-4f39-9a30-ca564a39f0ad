# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Main application entry point for the Delta Lake API service.

This module provides the FastAPI application with all endpoints,
monitoring, and background services configured.
"""

import logging
import os
from typing import Optional

from fastapi import FastAPI, Response
from fastapi.responses import PlainTextResponse

from data_marketplace.api.endpoints import create_app, get_duckdb_service
from data_marketplace.api.monitoring import monitoring
from data_marketplace.api.snapshot_manager import DeltaSnapshotMonitor
from data_marketplace.config.settings import Settings

logger = logging.getLogger(__name__)

# Global snapshot monitor
_snapshot_monitor: Optional[DeltaSnapshotMonitor] = None


def create_production_app() -> FastAPI:
    """Create the production FastAPI application with all services."""
    # Load settings
    settings = Settings()
    
    # Create the base app
    app = create_app(settings)
    
    # Initialize monitoring
    monitoring.prometheus.set_service_info(
        version="0.1.0",
        duckdb_version="1.3.0"  # This would be dynamically determined
    )
    
    # Add monitoring endpoints
    @app.get("/metrics")
    async def get_metrics():
        """Prometheus metrics endpoint."""
        metrics_text = monitoring.get_metrics_text()
        return PlainTextResponse(
            content=metrics_text,
            media_type=monitoring.get_metrics_content_type()
        )
    
    @app.get("/monitoring/status")
    async def get_monitoring_status():
        """Get detailed monitoring status."""
        service = get_duckdb_service()
        
        # Update connection metrics
        monitoring.prometheus.update_connection_metrics(
            active_connections=service.pool.pool_size,
            queries_in_flight=service.pool._active_queries
        )
        
        # Update table metrics
        table_info = {}
        for alias, config in service.attached_tables.items():
            try:
                info = service.get_table_info(alias)
                table_info[alias] = {
                    'path': info['path'],
                    'snapshot_version': None,  # Would need to be extracted from DuckDB
                    'last_refresh': 0  # Would need to be tracked
                }
            except Exception as e:
                logger.error(f"Failed to get info for table {alias}: {e}")
        
        monitoring.prometheus.update_table_metrics(
            attached_tables=len(service.attached_tables),
            table_info=table_info
        )
        
        return {
            "service_status": "healthy",
            "attached_tables": len(service.attached_tables),
            "active_queries": service.pool._active_queries,
            "pool_size": service.pool.pool_size,
            "snapshot_monitor_running": _snapshot_monitor._running if _snapshot_monitor else False,
            "monitored_tables": len(_snapshot_monitor.monitored_tables) if _snapshot_monitor else 0
        }
    
    @app.get("/monitoring/tables")
    async def get_table_monitoring_status():
        """Get snapshot monitoring status for all tables."""
        if not _snapshot_monitor:
            return {"error": "Snapshot monitor not initialized"}
        
        return _snapshot_monitor.get_status()
    
    # Initialize snapshot monitor on startup
    @app.on_event("startup")
    async def startup_snapshot_monitor():
        """Initialize and start the snapshot monitor."""
        global _snapshot_monitor
        
        service = get_duckdb_service()
        _snapshot_monitor = DeltaSnapshotMonitor(
            duckdb_service=service,
            check_interval=float(os.getenv("SNAPSHOT_CHECK_INTERVAL", "60"))
        )
        
        # Auto-add attached tables to monitoring
        for alias, config in service.attached_tables.items():
            _snapshot_monitor.add_table(alias, config.path)
        
        # Start the monitor
        _snapshot_monitor.start()
        logger.info("Snapshot monitor started")
    
    @app.on_event("shutdown")
    async def shutdown_snapshot_monitor():
        """Stop the snapshot monitor."""
        if _snapshot_monitor:
            _snapshot_monitor.stop()
            logger.info("Snapshot monitor stopped")
    
    return app


# Create the application instance
app = create_production_app()


if __name__ == "__main__":
    import uvicorn
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # Run the application
    uvicorn.run(
        "data_marketplace.api.main:app",
        host=os.getenv("HOST", "0.0.0.0"),
        port=int(os.getenv("PORT", "8000")),
        workers=int(os.getenv("WORKERS", "1")),
        reload=os.getenv("RELOAD", "false").lower() == "true",
        access_log=True
    )
