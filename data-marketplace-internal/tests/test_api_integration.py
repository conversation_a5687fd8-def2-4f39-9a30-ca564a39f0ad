# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Integration tests for the Delta Lake API service.

These tests validate the complete API functionality including:
- Query execution with Arrow and JSON responses
- Table management operations
- Concurrency and safety controls
- Monitoring and health checks
"""

import asyncio
import json
import os
import tempfile
import time
from pathlib import Path
from typing import Dict, Any

import pytest
import pyarrow as pa
import pyarrow.ipc as ipc
from fastapi.testclient import TestClient
from deltalake import write_deltalake

from data_marketplace.api.main import create_production_app
from data_marketplace.config.settings import Settings


@pytest.fixture
def temp_delta_table():
    """Create a temporary Delta Lake table for testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        table_path = Path(temp_dir) / "test_table"
        
        # Create test data
        data = pa.table({
            'id': [1, 2, 3, 4, 5],
            'name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
            'value': [10.5, 20.3, 30.1, 40.7, 50.9],
            'category': ['A', 'B', 'A', 'C', 'B']
        })
        
        # Write as Delta table
        write_deltalake(str(table_path), data, mode="overwrite")
        
        yield str(table_path)


@pytest.fixture
def test_settings():
    """Create test settings."""
    return Settings(
        debug=True,
        log_level="DEBUG"
    )


@pytest.fixture
def test_client(test_settings):
    """Create a test client for the API."""
    app = create_production_app()
    return TestClient(app)


class TestHealthAndStatus:
    """Test health checks and status endpoints."""
    
    def test_health_check(self, test_client):
        """Test the health check endpoint."""
        response = test_client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert "version" in data
        assert "duckdb_version" in data
        assert "attached_tables" in data
        assert "pool_size" in data
    
    def test_metrics_endpoint(self, test_client):
        """Test the Prometheus metrics endpoint."""
        response = test_client.get("/metrics")
        assert response.status_code == 200
        assert "text/plain" in response.headers["content-type"]
        
        # Check for some expected metrics
        content = response.text
        assert "duckdb_queries_total" in content or "Prometheus client not available" in content


class TestTableManagement:
    """Test table attachment and management."""
    
    def test_attach_local_table(self, test_client, temp_delta_table):
        """Test attaching a local Delta table."""
        response = test_client.post(
            "/tables/attach",
            params={
                "alias": "test_table",
                "path": temp_delta_table,
                "pin_snapshot": True
            }
        )
        assert response.status_code == 200
        assert "attached successfully" in response.json()["message"]
    
    def test_list_tables(self, test_client, temp_delta_table):
        """Test listing attached tables."""
        # First attach a table
        test_client.post(
            "/tables/attach",
            params={
                "alias": "test_table",
                "path": temp_delta_table,
                "pin_snapshot": True
            }
        )
        
        # List tables
        response = test_client.get("/tables")
        assert response.status_code == 200
        
        data = response.json()
        assert "tables" in data
        assert len(data["tables"]) >= 1
        
        # Check our table is in the list
        table_aliases = [table["alias"] for table in data["tables"]]
        assert "test_table" in table_aliases
    
    def test_get_table_info(self, test_client, temp_delta_table):
        """Test getting table information."""
        # Attach table first
        test_client.post(
            "/tables/attach",
            params={
                "alias": "test_table",
                "path": temp_delta_table,
                "pin_snapshot": True
            }
        )
        
        # Get table info
        response = test_client.get("/tables/test_table")
        assert response.status_code == 200
        
        data = response.json()
        assert data["alias"] == "test_table"
        assert data["path"] == temp_delta_table
        assert "schema" in data
        assert "row_count" in data
        assert data["pin_snapshot"] is True
    
    def test_detach_table(self, test_client, temp_delta_table):
        """Test detaching a table."""
        # Attach table first
        test_client.post(
            "/tables/attach",
            params={
                "alias": "test_table",
                "path": temp_delta_table,
                "pin_snapshot": True
            }
        )
        
        # Detach table
        response = test_client.delete("/tables/test_table")
        assert response.status_code == 200
        assert "detached successfully" in response.json()["message"]
        
        # Verify table is no longer listed
        response = test_client.get("/tables")
        table_aliases = [table["alias"] for table in response.json()["tables"]]
        assert "test_table" not in table_aliases


class TestQueryExecution:
    """Test query execution with different formats."""
    
    def test_simple_json_query(self, test_client, temp_delta_table):
        """Test a simple query with JSON response."""
        # Attach table
        test_client.post(
            "/tables/attach",
            params={
                "alias": "test_table",
                "path": temp_delta_table,
                "pin_snapshot": True
            }
        )
        
        # Execute query
        response = test_client.post(
            "/q",
            params={"fmt": "json"},
            json={
                "sql": "SELECT * FROM test_table ORDER BY id LIMIT 3",
                "timeout": 30.0
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "data" in data
        assert "metrics" in data
        assert "columns" in data
        
        # Check data
        assert len(data["data"]) == 3
        assert data["data"][0]["id"] == 1
        assert data["data"][0]["name"] == "Alice"
        
        # Check metrics
        metrics = data["metrics"]
        assert metrics["rows_returned"] == 3
        assert metrics["execution_time_ms"] > 0
    
    def test_arrow_query(self, test_client, temp_delta_table):
        """Test a query with Arrow response."""
        # Attach table
        test_client.post(
            "/tables/attach",
            params={
                "alias": "test_table",
                "path": temp_delta_table,
                "pin_snapshot": True
            }
        )
        
        # Execute query
        response = test_client.post(
            "/q",
            params={"fmt": "arrow"},
            json={
                "sql": "SELECT * FROM test_table ORDER BY id",
                "timeout": 30.0
            }
        )
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/vnd.apache.arrow.stream"
        
        # Check headers for metrics
        assert "X-Query-Time-Ms" in response.headers
        assert "X-Rows-Returned" in response.headers
        assert "X-Bytes-Scanned" in response.headers
        
        # Parse Arrow response
        buffer = response.content
        reader = ipc.open_stream(buffer)
        table = reader.read_all()
        
        assert len(table) == 5
        assert table.column_names == ['id', 'name', 'value', 'category']
    
    def test_query_with_parameters(self, test_client, temp_delta_table):
        """Test query with parameter substitution."""
        # Attach table
        test_client.post(
            "/tables/attach",
            params={
                "alias": "test_table",
                "path": temp_delta_table,
                "pin_snapshot": True
            }
        )
        
        # Execute parameterized query
        response = test_client.post(
            "/q",
            params={"fmt": "json"},
            json={
                "sql": "SELECT * FROM test_table WHERE category = {category} AND value > {min_value}",
                "parameters": {
                    "category": "A",
                    "min_value": 15.0
                },
                "timeout": 30.0
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Should return only Charlie (category A, value 30.1)
        assert len(data["data"]) == 1
        assert data["data"][0]["name"] == "Charlie"
        assert data["data"][0]["category"] == "A"
    
    def test_query_safety_validation(self, test_client, temp_delta_table):
        """Test query safety validation."""
        # Attach table
        test_client.post(
            "/tables/attach",
            params={
                "alias": "test_table",
                "path": temp_delta_table,
                "pin_snapshot": True
            }
        )
        
        # Try dangerous queries
        dangerous_queries = [
            "DROP TABLE test_table",
            "DELETE FROM test_table",
            "INSERT INTO test_table VALUES (6, 'Hacker', 0, 'X')",
            "CREATE TABLE evil AS SELECT * FROM test_table"
        ]
        
        for sql in dangerous_queries:
            response = test_client.post(
                "/q",
                params={"fmt": "json"},
                json={"sql": sql}
            )
            assert response.status_code == 400
            assert "ValidationError" in response.json()["detail"]["error_type"]


class TestNamedQueries:
    """Test named query functionality."""
    
    def test_list_named_queries(self, test_client):
        """Test listing available named queries."""
        response = test_client.get("/q/named")
        assert response.status_code == 200
        
        data = response.json()
        assert "queries" in data
        assert len(data["queries"]) > 0
        
        # Check for expected named queries
        query_names = [q["name"] for q in data["queries"]]
        assert "recent_scenes" in query_names


class TestConcurrencyAndLimits:
    """Test concurrency controls and safety limits."""
    
    def test_concurrent_queries(self, test_client, temp_delta_table):
        """Test handling of concurrent queries."""
        # Attach table
        test_client.post(
            "/tables/attach",
            params={
                "alias": "test_table",
                "path": temp_delta_table,
                "pin_snapshot": True
            }
        )
        
        # Execute multiple queries concurrently
        import concurrent.futures
        
        def execute_query():
            return test_client.post(
                "/q",
                params={"fmt": "json"},
                json={
                    "sql": "SELECT COUNT(*) as count FROM test_table",
                    "timeout": 30.0
                }
            )
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(execute_query) for _ in range(5)]
            results = [future.result() for future in futures]
        
        # All queries should succeed (within limits)
        for response in results:
            assert response.status_code in [200, 429]  # Success or rate limited
    
    def test_query_timeout(self, test_client, temp_delta_table):
        """Test query timeout handling."""
        # Attach table
        test_client.post(
            "/tables/attach",
            params={
                "alias": "test_table",
                "path": temp_delta_table,
                "pin_snapshot": True
            }
        )
        
        # Execute query with very short timeout
        response = test_client.post(
            "/q",
            params={"fmt": "json"},
            json={
                "sql": "SELECT * FROM test_table",
                "timeout": 0.001  # Very short timeout
            }
        )
        
        # Should either succeed quickly or timeout
        assert response.status_code in [200, 408]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
