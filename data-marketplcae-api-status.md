# Data Marketplace API Status

## What We Have Built

### Data Infrastructure
- Delta Lake table with STAC metadata + COG headers (one row per band)
- Spatial indexing (S2 cells) and temporal partitioning (year/month)
- COG technical metadata: tile offsets, byte counts, transforms, compression details

### API Service
- FastAPI with DuckDB backend for fast queries
- Apache Arrow streaming responses
- Basic endpoints: `/q` for SQL queries, `/health`, `/tables/*`
- Connection pooling and safety controls

### Rasteret Library (User Side)
- Creates local parquet files with STAC + COG headers
- Efficient COG tile reading with HTTP/2 multiplexing
- Simple interface: `get_xarray(geometries, bands, filters)`
- Replaces repeated COG header requests with local cache

## The Problem

**Current API**: Users must write SQL queries
```bash
curl -X POST "/q" -d '{"sql": "SELECT scene_id, cog_href FROM table WHERE ..."}'
```

**What Users Want**: Simple REST parameters
```bash
curl "/scenes?bbox=77.55,13.01,77.58,13.08&date_range=2024-01-01,2024-12-31&bands=B4,B5"
```

## User Workflow (Rasteret)

1. **Collection Creation**: `processor.create_collection(bbox, date_range, filters)`
   - Fetches STAC items from catalogs
   - Downloads COG headers for each band
   - Saves to local parquet file

2. **Data Query**: `processor.get_xarray(geometries, bands, filters)`
   - Filters local parquet for matching scenes
   - Reads COG tiles using cached headers
   - Returns xarray dataset

## What's Missing

### For Users
- Simple REST endpoints instead of SQL
- Direct integration with rasteret workflows
- COG header access without local parquet creation

### For Us (Maintainers)
- User-friendly API design
- Authentication (mentioned but not implemented)
- Proper error handling for common use cases

## Next Steps

### Immediate (This Week)
1. Add simple scene discovery endpoint: `GET /scenes`
2. Add COG metadata endpoint: `GET /scenes/{id}/bands/{band}`
3. Test with rasteret integration

### Soon (Next Month)
1. Authentication layer
2. Rate limiting
3. Better error messages

## Key Insight

The API should **complement** rasteret, not replace it. Users will still want local caching for repeated analysis, but they shouldn't need to create full collections just to get COG headers for a few scenes.
